import React from 'react'
import Image from 'next/image'

interface NPINewsHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPINewsHeroBlock: React.FC<NPINewsHeroProps> = ({
  title = 'News & Media',
  backgroundImage = '/assets/Gemini_Generated_Image_afyc4tafyc4tafyc.png',
}) => {
  return (
    <section className="relative min-h-[95vh] max-h-[95vh] overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with bright color variants */}
      <div className="absolute inset-0 bg-gradient-to-b from-[var(--npi-brown)]/30 via-[var(--npi-reddish-brown)]/50 to-[var(--npi-blue)]/70" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight npi-text-white">
          {title}
        </h1>
      </div>

      {/* Bottom Left Text with bright colors */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[var(--npi-cream)]/90 backdrop-blur-md p-6 border-l-4 border-[var(--npi-brown-light)]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed npi-text-black">
            Stay informed about the latest developments and insights from Kenya&apos;s natural
            products sector.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with bright colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[var(--npi-cream-light)]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[var(--npi-cream-medium)]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 npi-text-brown">Latest Updates</h3>
          <p className="text-sm sm:text-base mb-4 npi-text-brown">
            Discover stories of innovation, community impact, and sustainable development across
            Kenya.
          </p>
          <a
            href="#latest-news"
            className="npi-text-reddish-brown hover:npi-text-blue text-sm font-medium transition-colors"
          >
            Read Latest News &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
