import React from 'react'
import Image from 'next/image'

interface NPISuccessStoriesHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPISuccessStoriesHeroBlock: React.FC<NPISuccessStoriesHeroProps> = ({
  title = 'Success Stories',
  backgroundImage = '/assets/product 1.jpg',
}) => {
  return (
    <section className="relative min-h-[95vh] max-h-[95vh] overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full z-0 bg-[#8A3E25]">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with bright color variants */}
      <div className="absolute inset-0 bg-gradient-to-b from-[var(--npi-brown)]/30 via-[var(--npi-reddish-brown)]/50 to-[var(--npi-blue)]/70 z-10" />

      {/* Top Left Title */}
      <div className="absolute top-24 left-8 z-30">
        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold npi-text-white mb-4">{title}</h1>
        <div className="w-24 h-1 bg-[var(--npi-cream-light)]" />
      </div>

      {/* Bottom Right Feature Card with bright colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[var(--npi-cream-light)]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[var(--npi-cream-medium)]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 npi-text-brown">Inspiring Stories</h3>
          <p className="text-sm sm:text-base mb-4 npi-text-brown">
            Discover real impact stories of transformation, empowerment, and sustainable development
            across Kenya&apos;s communities.
          </p>
          <a
            href="#success-stories"
            className="npi-text-reddish-brown hover:npi-text-blue text-sm font-medium transition-colors"
          >
            Explore Success Stories &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
