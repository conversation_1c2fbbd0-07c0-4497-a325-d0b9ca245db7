'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { ArrowRight, Target, Eye, Heart, Users, MapPin, TrendingUp } from 'lucide-react'
import { motion } from 'framer-motion'

interface NPIIntroductionProps {
  title?: string
  description?: string
  content?: string
}

export const NPIIntroductionBlock: React.FC<NPIIntroductionProps> = ({
  title = 'About the Natural Products Industry Initiative',
  description = "Transforming Kenya's Vision 2030 into reality through sustainable natural products development.",
  content = "The Natural Products Industry (NPI) Initiative is a groundbreaking multi-agency program designed to harness Kenya's rich indigenous knowledge and natural resources for sustainable economic development. Aligned with Kenya Vision 2030 and the Medium Term Plan IV (MTP IV), NPI serves as a catalyst for transforming traditional knowledge into market-ready products while preserving cultural heritage and empowering local communities.",
}) => {
  const highlights = [
    {
      icon: <Target className="w-6 h-6" />,
      title: 'Strategic Alignment',
      description:
        'Directly supports Kenya Vision 2030 and MTP IV objectives for sustainable development.',
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: 'Innovation Focus',
      description:
        'Combines traditional knowledge with modern science and technology for product development.',
    },
    {
      icon: <Heart className="w-6 h-6" />,
      title: 'Community Centered',
      description:
        'Ensures local communities are central to and benefit from all natural product initiatives.',
    },
  ]

  return (
    <NPISection className="bg-gradient-to-br from-[#FFFFFF] via-[#EFE3BA] to-[#725242] relative overflow-hidden -mt-4 pt-8">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-[#25718A]/20 blur-3xl"
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#8A3E25]/15 blur-3xl"
          animate={{
            x: [0, -40, 0],
            y: [0, 25, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center px-6 py-3 bg-[#4C6444]/20 border-2 border-[#4C6444] text-[#34170D] text-sm font-medium mb-8 rounded-lg"
          >
            <span className="w-2 h-2 bg-[#4C6444] mr-3 rounded-full"></span>
            Natural Products Industry Initiative
          </motion.div>
          <NPISectionTitle className="text-[#34170D] font-bold leading-[1.1] tracking-[-0.02em]">
            {title}
          </NPISectionTitle>
          <NPISectionDescription className="text-[#46372A] max-w-3xl mx-auto font-light leading-[1.6]">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Main Content */}
        <div className="grid lg:grid-cols-3 gap-6 mb-16">
          {/* Content Column */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="bg-white/80 backdrop-blur-sm p-6 shadow-lg border border-white/20 h-[520px] w-full flex flex-col"
            >
              <div className="prose prose-lg max-w-none mb-6 flex-shrink-0">
                <p className="text-[#46372A] leading-[1.6] font-npi text-base font-light">
                  {content}
                </p>
              </div>

              <div className="grid gap-3 flex-1 overflow-hidden">
                {highlights.map((highlight, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    className="flex gap-3 p-3 bg-gradient-to-r from-[#E5E1DC] to-[#CEC9BC] border-2 border-[#8D8F78]/30 hover:border-[#4C6444] transition-all duration-300 group h-[90px] w-full"
                  >
                    <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-[#4C6444] to-[#102820] flex items-center justify-center text-[#E5E1DC] shadow-lg group-hover:scale-110 transition-transform duration-300">
                      {highlight.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-sm mb-1 font-npi text-[#34170D] group-hover:text-[#6E3C19] transition-colors truncate">
                        {highlight.title}
                      </h3>
                      <p className="text-[#46372A] font-npi leading-tight text-xs line-clamp-3">
                        {highlight.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-3 mt-4 flex-shrink-0">
                <NPIButton
                  asChild
                  className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2 shadow-md hover:shadow-lg transition-all duration-200 text-sm"
                >
                  <Link href="/about">
                    Learn More About NPI <ArrowRight className="w-3 h-3 ml-2" />
                  </Link>
                </NPIButton>

                <NPIButton
                  asChild
                  className="border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground font-medium px-4 py-2 transition-all duration-200 text-sm"
                >
                  <Link href="/strategic-pillars">Explore Strategic Pillars</Link>
                </NPIButton>
              </div>
            </motion.div>
          </div>

          {/* Statistics Sidebar */}
          <div className="space-y-3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-gradient-to-br from-[#4C6444] to-[#102820] text-[#E5E1DC] p-4 shadow-lg h-[165px] w-full flex flex-col justify-center"
            >
              <div className="flex items-center gap-3 mb-4">
                <MapPin className="w-6 h-6" />
                <h3 className="font-semibold text-lg">47 Counties</h3>
              </div>
              <div className="text-3xl font-bold mb-2">Nationwide</div>
              <p className="text-[#E5E1DC]/80">Complete coverage across Kenya</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-gradient-to-br from-[#A7795E] to-[#8A6240] text-[#E5E1DC] p-4 shadow-lg h-[165px] w-full flex flex-col justify-center"
            >
              <div className="flex items-center gap-3 mb-4">
                <Users className="w-6 h-6" />
                <h3 className="font-semibold text-lg">2,500+ Members</h3>
              </div>
              <div className="text-3xl font-bold mb-2">Community</div>
              <p className="text-[#E5E1DC]/80">Directly engaged participants</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="bg-gradient-to-br from-[#6E3C19] to-[#34170D] border-2 border-[#8A6240] p-4 shadow-lg h-[165px] w-full flex flex-col justify-center"
            >
              <div className="flex items-center gap-3 mb-4">
                <TrendingUp className="w-6 h-6 text-[#E5E1DC]" />
                <h3 className="font-semibold text-lg text-[#E5E1DC]">85% Growth</h3>
              </div>
              <div className="text-3xl font-bold mb-2 text-[#E5E1DC]">Income</div>
              <p className="text-[#E5E1DC]/80">Average increase for communities</p>
            </motion.div>
          </div>
        </div>

        {/* Key Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="grid md:grid-cols-2 gap-6 mb-8"
        >
          <div className="text-center bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] p-6 border-2 border-[#4C6444]/30 shadow-lg h-[165px] w-full flex flex-col justify-center">
            <div className="text-5xl font-bold text-[#34170D] mb-2 font-npi">1,200+</div>
            <div className="text-lg text-[#46372A] font-npi font-medium">
              Knowledge Assets Documented
            </div>
          </div>
          <div className="text-center bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] p-6 border-2 border-[#A7795E]/30 shadow-lg h-[165px] w-full flex flex-col justify-center">
            <div className="text-5xl font-bold text-[#34170D] mb-2 font-npi">150+</div>
            <div className="text-lg text-[#46372A] font-npi font-medium">
              Investment Opportunities
            </div>
          </div>
        </motion.div>

        {/* Partners Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 1.0 }}
          className="bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] p-6 border-2 border-[#8D8F78]/30 shadow-lg h-[165px] w-full flex flex-col justify-center"
        >
          <h3 className="text-[#34170D] text-center text-xl font-semibold mb-4">
            Key Implementing Partners
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-[#46372A] font-npi">
            <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#CABA9C] to-[#8A6240]/20 border-2 border-[#4C6444]/30 hover:border-[#4C6444] transition-colors group h-[70px] w-full">
              <div className="w-3 h-3 bg-[#4C6444] group-hover:scale-125 transition-transform rounded-full flex-shrink-0"></div>
              <span className="text-sm font-medium">Organiser 1</span>
            </div>
            <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#CABA9C] to-[#8A6240]/20 border-2 border-[#A7795E]/30 hover:border-[#A7795E] transition-colors group h-[70px] w-full">
              <div className="w-3 h-3 bg-[#A7795E] group-hover:scale-125 transition-transform rounded-full flex-shrink-0"></div>
              <span className="text-sm font-medium">Organiser 2</span>
            </div>
            <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#CABA9C] to-[#8A6240]/20 border-2 border-[#6E3C19]/30 hover:border-[#6E3C19] transition-colors group h-[70px] w-full">
              <div className="w-3 h-3 bg-[#6E3C19] group-hover:scale-125 transition-transform rounded-full flex-shrink-0"></div>
              <span className="text-sm font-medium">Organiser 3</span>
            </div>
            <div className="flex items-center gap-2 p-3 bg-gradient-to-r from-[#CABA9C] to-[#8A6240]/20 border-2 border-[#8D8F78]/30 hover:border-[#8D8F78] transition-colors group h-[70px] w-full">
              <div className="w-3 h-3 bg-[#8D8F78] group-hover:scale-125 transition-transform rounded-full flex-shrink-0"></div>
              <span className="text-sm font-medium">Organiser 4</span>
            </div>
          </div>
        </motion.div>
      </div>
    </NPISection>
  )
}
