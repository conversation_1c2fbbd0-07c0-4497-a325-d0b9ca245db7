import React from 'react'
import Image from 'next/image'

interface NPIOperationsHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIOperationsHeroBlock: React.FC<NPIOperationsHeroProps> = ({
  title = 'Operations & Structure',
  subtitle = 'Multi-agency framework driving sustainable development',
  backgroundImage = '/assets/background.jpg',
}) => {
  return (
    <section className="relative min-h-[85vh] max-h-[95vh] overflow-hidden -mt-24 pt-24">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Operations and Structure background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with 6-color palette */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#8A3E25]/30 via-[#725242]/50 to-[#8A3E25]/70" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white mb-4">
          {title}
        </h1>
        {subtitle && (
          <p className="text-lg sm:text-xl md:text-2xl text-white/90 font-medium">{subtitle}</p>
        )}
      </div>

      {/* Bottom Left Text with 6-color palette */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#EFE3BA]/90 backdrop-blur-md p-6 border-l-4 border-[#725242]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-black">
            Collaborative partnerships driving innovation and sustainable development across
            Kenya&apos;s natural products sector.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with 6-color palette */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#FFFFFF]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#725242]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#25718A]">
            Multi-Agency Approach
          </h3>
          <p className="text-sm sm:text-base mb-4 text-[#725242]">
            Leveraging expertise from multiple institutions to maximize impact and ensure
            <span className="text-[#25718A] font-semibold">comprehensive coverage</span>.
          </p>
          <a
            href="#"
            className="bg-[#25718A] text-white hover:bg-[#8A3E25] text-sm font-medium transition-colors px-6 py-2 shadow-md border-2 border-[#25718A] hover:border-[#8A3E25] inline-block"
          >
            Learn More &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
