import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { NPIScrollToTop } from '@/components/ui/npi-scroll-to-top'
import PageClient from './page.client'

// About page components are now organized by page structure
// Main about page uses: NPIAboutHeroBlock, NPIHistoryTimelineBlock from about/main
// Plus shared components: NPIIntroductionBlock, NPIMissionVisionBlock

export const metadata: Metadata = {
  title: 'About NPI - Natural Products Industry Initiative',
  description:
    "Learn about the Natural Products Industry Initiative, our mission, vision, strategic alignment, and multi-agency approach to transforming Kenya's natural heritage into sustainable economic opportunities.",
}

const aboutPageLayout = [
  {
    blockType: 'npiAboutHero' as const,
  },
  {
    blockType: 'npiIntroduction' as const,
  },
  {
    blockType: 'npiHistoryTimeline' as const,
  },
  {
    blockType: 'npiMissionVision' as const,
  },
]

export default function AboutPage() {
  return (
    <>
      <PageClient />
      <article className="pb-12" style={{ scrollBehavior: 'smooth' }}>
        {aboutPageLayout.map((block, index) => (
          <section
            key={index}
            className={`
              ${index === 0 ? '' : '-mt-1'}
              relative
              ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
            `}
          >
            <RenderBlocks blocks={[block]} />
          </section>
        ))}
      </article>
      <NPIScrollToTop showAfter={400} />
    </>
  )
}
