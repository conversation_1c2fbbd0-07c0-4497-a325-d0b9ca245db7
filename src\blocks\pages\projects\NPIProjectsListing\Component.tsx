'use client'

import React, { useState } from 'react'
import {
  NPIS<PERSON>tion,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardDescription,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { X, MapPin, Calendar, Users, DollarSign, Target, Handshake } from 'lucide-react'

interface Project {
  id: string
  title: string
  description: string
  category: string
  pillar: string
  location: string
  duration: string
  status: 'active' | 'completed' | 'upcoming'
  participants: number
  budget: string
  objectives: string[]
  partners: string[]
  image: string
}

interface NPIProjectsListingProps {
  title?: string
  description?: string
  projects?: Project[]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-[#25718A] text-white'
    case 'completed':
      return 'bg-[#8A3E25] text-white'
    case 'upcoming':
      return 'bg-[#725242] text-white'
    default:
      return 'bg-[#725242] text-white'
  }
}

const getCategoryColor = (category: string) => {
  const colors = [
    'from-[#8A3E25] to-[#725242]',
    'from-[#725242] to-[#8A3E25]',
    'from-[#25718A] to-[#725242]',
    'from-[#8A3E25] to-[#25718A]',
    'from-[#725242] to-[#25718A]',
  ]
  const index = category.length % colors.length
  return colors[index]
}

export const NPIProjectsListingBlock: React.FC<NPIProjectsListingProps> = ({
  title = 'Our Projects & Initiatives',
  description = "Comprehensive projects transforming Kenya's natural products landscape through community-driven innovation, capacity building, and sustainable development.",
  projects = [
    {
      id: 'knowledge-documentation',
      title: 'Indigenous Knowledge Documentation Project',
      description:
        "Systematic documentation of traditional knowledge systems across Kenya's 47 counties, creating a comprehensive digital repository for preservation and access.",
      category: 'Knowledge Preservation',
      pillar: 'Indigenous Knowledge Documentation',
      location: 'All 47 Counties',
      duration: '2022-2025',
      status: 'active',
      participants: 2500,
      budget: 'KES 150M',
      objectives: [
        'Document traditional medicinal practices',
        'Create digital knowledge repository',
        'Train community knowledge keepers',
        'Establish preservation protocols',
      ],
      partners: ['University of Nairobi', 'Kenya Medical Research Institute', 'County Governments'],
      image: '/assets/product 1.jpg',
    },
    {
      id: 'community-empowerment',
      title: 'Community-Based Natural Products Development',
      description:
        'Empowering local communities to develop sustainable natural product enterprises through training, capacity building, and market linkage support.',
      category: 'Community Development',
      pillar: 'Capacity Building',
      location: 'Central & Eastern Kenya',
      duration: '2023-2026',
      status: 'active',
      participants: 1800,
      budget: 'KES 120M',
      objectives: [
        'Establish community enterprises',
        'Provide technical training',
        'Create market linkages',
        'Build processing facilities',
      ],
      partners: ['Kenya Association of Manufacturers', 'USAID', 'Local CBOs'],
      image: '/assets/product 2.jpg',
    },
    {
      id: 'innovation-hub',
      title: 'Natural Products Innovation Hub',
      description:
        'State-of-the-art research and development facility for natural products innovation, product testing, and commercialization support.',
      category: 'Innovation & Research',
      pillar: 'Product Development',
      location: 'Nairobi',
      duration: '2024-2027',
      status: 'upcoming',
      participants: 500,
      budget: 'KES 200M',
      objectives: [
        'Establish R&D facilities',
        'Support product development',
        'Provide testing services',
        'Facilitate commercialization',
      ],
      partners: [
        'Kenya Bureau of Standards',
        'International Development Partners',
        'Private Sector',
      ],
      image: '/assets/product 3.jpg',
    },
    {
      id: 'market-development',
      title: 'Natural Products Market Development Initiative',
      description:
        'Comprehensive market development program to create sustainable value chains and expand market access for natural products.',
      category: 'Market Development',
      pillar: 'Value Chain Development',
      location: 'Nationwide',
      duration: '2023-2025',
      status: 'active',
      participants: 3200,
      budget: 'KES 180M',
      objectives: [
        'Develop value chains',
        'Create market platforms',
        'Establish quality standards',
        'Build export capacity',
      ],
      partners: ['Export Promotion Council', 'Kenya Trade Network Agency', 'Regional Buyers'],
      image: '/assets/product 4.jpg',
    },
    {
      id: 'youth-engagement',
      title: 'Youth in Natural Products Initiative',
      description:
        'Engaging young entrepreneurs in natural products development through mentorship, funding, and business development support.',
      category: 'Youth Development',
      pillar: 'Capacity Building',
      location: 'Urban Centers',
      duration: '2024-2026',
      status: 'upcoming',
      participants: 1000,
      budget: 'KES 80M',
      objectives: [
        'Train young entrepreneurs',
        'Provide startup funding',
        'Offer mentorship programs',
        'Create youth networks',
      ],
      partners: ['Kenya Youth Business Trust', 'Universities', 'Youth Organizations'],
      image: '/assets/product 5.jpg',
    },
    {
      id: 'research-collaboration',
      title: 'International Research Collaboration Program',
      description:
        'Building partnerships with international research institutions to advance natural products research and development.',
      category: 'Research Collaboration',
      pillar: 'Knowledge Documentation',
      location: 'Multiple Countries',
      duration: '2022-2024',
      status: 'completed',
      participants: 200,
      budget: 'KES 60M',
      objectives: [
        'Establish research partnerships',
        'Exchange knowledge and expertise',
        'Develop joint research projects',
        'Build institutional capacity',
      ],
      partners: ['International Universities', 'Research Institutes', 'Development Partners'],
      image: '/assets/product 6.jpg',
    },
    {
      id: 'policy-framework',
      title: 'Natural Products Policy Framework Development',
      description:
        'Developing comprehensive policy frameworks to support the growth and regulation of the natural products industry.',
      category: 'Policy Development',
      pillar: 'Regulatory Framework',
      location: 'National',
      duration: '2023-2024',
      status: 'completed',
      participants: 150,
      budget: 'KES 40M',
      objectives: [
        'Develop policy frameworks',
        'Engage stakeholders',
        'Create regulatory guidelines',
        'Facilitate implementation',
      ],
      partners: ['Ministry of Health', 'Parliament', 'Industry Associations'],
      image: '/assets/product 7.jpg',
    },
    {
      id: 'technology-transfer',
      title: 'Technology Transfer and Commercialization',
      description:
        'Facilitating the transfer of research innovations to commercial applications through partnerships and support programs.',
      category: 'Technology Transfer',
      pillar: 'Product Development',
      location: 'Nairobi & Mombasa',
      duration: '2024-2027',
      status: 'active',
      participants: 800,
      budget: 'KES 100M',
      objectives: [
        'Transfer research to market',
        'Support commercialization',
        'Build industry partnerships',
        'Create innovation ecosystem',
      ],
      partners: ['Technology Companies', 'Research Institutions', 'Venture Capital'],
      image: '/assets/product 8.jpg',
    },
  ],
}) => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [filter, setFilter] = useState<string>('all')

  const categories = ['all', ...Array.from(new Set(projects.map((p) => p.category)))]
  const filteredProjects =
    filter === 'all' ? projects : projects.filter((p) => p.category === filter)

  return (
    <NPISection className="py-24 bg-gradient-to-b from-[#FFFFFF] to-[#EFE3BA]">
      <div className="container mx-auto px-4">
        <NPISectionHeader className="text-center mb-16">
          <NPISectionTitle className="text-black mb-4">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <NPIButton
              key={category}
              variant={filter === category ? 'primary' : 'outline'}
              onClick={() => setFilter(category)}
              className={`${
                filter === category
                  ? 'bg-gradient-to-r from-[#102820] to-[#4C6444] text-[#E5E1DC]'
                  : 'border-[#46372A] text-[#46372A] hover:bg-[#46372A] hover:text-[#E5E1DC]'
              } transition-all duration-300`}
            >
              {category === 'all' ? 'All Projects' : category}
            </NPIButton>
          ))}
        </div>

        {/* Projects Grid - 4 cards per row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -10 }}
              className="h-full"
            >
              <NPICard className="h-full overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-[#725242]/30">
                <div className="relative h-48 w-full">
                  <Image src={project.image} alt={project.title} fill className="object-cover" />
                  <div className="absolute top-4 left-4">
                    <span
                      className={`px-3 py-1 text-xs font-medium ${getStatusColor(project.status)}`}
                    >
                      {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span
                      className={`px-2 py-1 text-xs font-bold text-white bg-gradient-to-r ${getCategoryColor(project.category)}`}
                    >
                      {project.category}
                    </span>
                  </div>
                </div>

                <NPICardContent className="p-6 flex-1 flex flex-col">
                  <NPICardHeader className="mb-4">
                    <NPICardTitle className="text-black text-lg font-bold mb-2 line-clamp-2">
                      {project.title}
                    </NPICardTitle>
                    <NPICardDescription className="text-[#725242] text-sm line-clamp-3">
                      {project.description}
                    </NPICardDescription>
                  </NPICardHeader>

                  <div className="space-y-2 mb-4 flex-1">
                    <div className="flex items-center gap-2 text-xs text-[#46372A]">
                      <MapPin className="w-4 h-4 text-[#6E3C19]" />
                      <span>{project.location}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-[#46372A]">
                      <Calendar className="w-4 h-4 text-[#6E3C19]" />
                      <span>{project.duration}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-[#46372A]">
                      <Users className="w-4 h-4 text-[#6E3C19]" />
                      <span>{project.participants.toLocaleString()} participants</span>
                    </div>
                  </div>

                  <NPICardFooter className="pt-4">
                    <NPIButton
                      onClick={() => setSelectedProject(project)}
                      className="w-full bg-gradient-to-r from-[#6E3C19] to-[#A7795E] hover:from-[#8A6240] hover:to-[#CABA9C] text-[#E5E1DC] font-bold transition-all duration-300"
                    >
                      View Details
                    </NPIButton>
                  </NPICardFooter>
                </NPICardContent>
              </NPICard>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              className="bg-[#E5E1DC] max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="relative h-64 w-full">
                <Image
                  src={selectedProject.image}
                  alt={selectedProject.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                <button
                  onClick={() => setSelectedProject(null)}
                  className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white p-2 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
                <div className="absolute bottom-6 left-6 text-white">
                  <h2 className="text-2xl md:text-3xl font-bold mb-2">{selectedProject.title}</h2>
                  <div className="flex items-center gap-4">
                    <span
                      className={`px-3 py-1 text-sm font-medium ${getStatusColor(selectedProject.status)}`}
                    >
                      {selectedProject.status.charAt(0).toUpperCase() +
                        selectedProject.status.slice(1)}
                    </span>
                    <span
                      className={`px-3 py-1 text-sm font-bold text-[#E5E1DC] bg-gradient-to-r ${getCategoryColor(selectedProject.category)}`}
                    >
                      {selectedProject.category}
                    </span>
                  </div>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Left Column */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-[#141311] mb-3">Project Overview</h3>
                      <p className="text-[#46372A] leading-relaxed">
                        {selectedProject.description}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-[#141311] mb-3">Key Details</h3>
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <MapPin className="w-5 h-5 text-[#6E3C19]" />
                          <span className="text-[#46372A]">
                            <strong>Location:</strong> {selectedProject.location}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Calendar className="w-5 h-5 text-[#6E3C19]" />
                          <span className="text-[#46372A]">
                            <strong>Duration:</strong> {selectedProject.duration}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Users className="w-5 h-5 text-[#6E3C19]" />
                          <span className="text-[#46372A]">
                            <strong>Participants:</strong>{' '}
                            {selectedProject.participants.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <DollarSign className="w-5 h-5 text-[#6E3C19]" />
                          <span className="text-[#46372A]">
                            <strong>Budget:</strong> {selectedProject.budget}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-[#141311] mb-3 flex items-center gap-2">
                        <Target className="w-5 h-5 text-[#6E3C19]" />
                        Key Objectives
                      </h3>
                      <ul className="space-y-2">
                        {selectedProject.objectives.map((objective, index) => (
                          <li key={index} className="flex items-start gap-2 text-[#46372A]">
                            <span className="w-2 h-2 bg-[#6E3C19] mt-2 flex-shrink-0"></span>
                            <span>{objective}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-[#141311] mb-3 flex items-center gap-2">
                        <Handshake className="w-5 h-5 text-[#6E3C19]" />
                        Key Partners
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.partners.map((partner, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-gradient-to-r from-[#8A6240] to-[#CABA9C] text-[#E5E1DC] text-sm font-medium"
                          >
                            {partner}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="pt-4">
                      <NPIButton
                        className="w-full bg-gradient-to-r from-[#102820] to-[#4C6444] hover:from-[#34170D] hover:via-[#6E3C19] hover:to-[#A7795E] text-[#E5E1DC] font-bold py-3"
                        onClick={() => setSelectedProject(null)}
                      >
                        Close Details
                      </NPIButton>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </NPISection>
  )
}
