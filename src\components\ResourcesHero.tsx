'use client'

import React from 'react'
import Link from 'next/link'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import { motion } from 'framer-motion'

export const ResourcesHero: React.FC = () => {
  return (
    <NPIParallaxHero
      backgroundVideo="/assets/AZhkc7L4RdmED9W-0kozFg-AZhkc7L4FR24vnG-HzFjNQ (1).mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[85vh] max-h-[95vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt-26 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base npi-text-cream font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              Natural Products Industry Initiative
            </motion.p>

            {/* Main title */}
            <motion.h2
              className="text-xl md:text-2xl lg:text-3xl font-bold leading-tight tracking-[-0.01em] npi-text-cream"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              Comprehensive Knowledge Hub
            </motion.h2>

            {/* CTA Buttons moved to top left */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="primary"
                  className="bg-gradient-to-r from-[var(--npi-brown)] to-[var(--npi-reddish-brown)] hover:from-[var(--npi-reddish-brown)] hover:to-[var(--npi-brown)] npi-text-white font-bold px-8 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-[var(--npi-cream)]/40 hover:border-[var(--npi-blue)]/60"
                >
                  <Link href="#featured-resources" className="flex items-center gap-2">
                    Browse Resources
                    <motion.span
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[var(--npi-blue)] bg-gradient-to-r from-[var(--npi-cream)]/30 to-[var(--npi-blue)]/25 npi-text-white hover:bg-gradient-to-r hover:from-[var(--npi-blue)] hover:to-[var(--npi-cream)] hover:npi-text-black backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold hover:border-[var(--npi-cream)]"
                >
                  <Link href="/contact" className="flex items-center gap-2">
                    Submit Resource
                    <motion.span
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom Right Section - Aligned with navbar container */}
        <motion.div
          className="absolute bottom-0 right-0 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl ml-auto"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-xl text-right">
            <motion.h1
              className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 leading-[1.1] tracking-[-0.02em]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Access Research, Publications &{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[var(--npi-brown)] via-[var(--npi-cream)] via-[var(--npi-blue)] to-[var(--npi-reddish-brown)]">
                Educational Materials
              </span>
            </motion.h1>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
