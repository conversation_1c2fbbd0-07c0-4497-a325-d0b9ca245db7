'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import {
  FileText,
  Download,
  Calendar,
  Eye,
  Search,
  Filter,
  BookOpen,
  Video,
  Image,
} from 'lucide-react'

interface Resource {
  id: string
  title: string
  description: string
  type: 'report' | 'guide' | 'policy' | 'research' | 'video' | 'presentation' | 'toolkit'
  category: string
  date: string
  fileSize?: string
  downloadCount: number
  featured?: boolean
  url: string
  previewUrl?: string
  tags: string[]
}

interface NPIResourcesLibraryProps {
  title?: string
  description?: string
  resources?: Resource[]
}

export const NPIResourcesLibraryBlock: React.FC<NPIResourcesLibraryProps> = ({
  title = 'Resources & Publications',
  description = 'Access comprehensive resources, research publications, policy documents, and educational materials to support natural products development and traditional knowledge preservation.',
  resources = [
    {
      id: 'npi-strategic-plan-2024',
      title: 'NPI Strategic Plan 2024-2028',
      description:
        "Comprehensive strategic framework outlining NPI's vision, objectives, and implementation roadmap for the next five years.",
      type: 'report',
      category: 'Strategic Planning',
      date: '2024-01-15',
      fileSize: '2.5 MB',
      downloadCount: 1250,
      featured: true,
      url: '/resources/npi-strategic-plan-2024.pdf',
      previewUrl: '/resources/previews/strategic-plan-preview.pdf',
      tags: ['strategy', 'planning', 'vision', 'roadmap'],
    },
    {
      id: 'traditional-knowledge-documentation-guide',
      title: 'Traditional Knowledge Documentation Guide',
      description:
        'Step-by-step guide for documenting indigenous knowledge while respecting community protocols and intellectual property rights.',
      type: 'guide',
      category: 'Knowledge Preservation',
      date: '2023-11-20',
      fileSize: '1.8 MB',
      downloadCount: 890,
      featured: false,
      url: '/resources/tk-documentation-guide.pdf',
      tags: ['documentation', 'traditional knowledge', 'community protocols'],
    },
    {
      id: 'natural-products-market-analysis',
      title: 'Kenya Natural Products Market Analysis 2023',
      description:
        "Comprehensive analysis of Kenya's natural products market, including trends, opportunities, and competitive landscape.",
      type: 'research',
      category: 'Market Research',
      date: '2023-09-10',
      fileSize: '3.2 MB',
      downloadCount: 675,
      featured: true,
      url: '/resources/market-analysis-2023.pdf',
      tags: ['market analysis', 'trends', 'opportunities', 'competitive landscape'],
    },
    {
      id: 'community-benefit-sharing-framework',
      title: 'Community Benefit-Sharing Framework',
      description:
        'Policy framework ensuring fair and equitable benefit-sharing from traditional knowledge commercialization.',
      type: 'policy',
      category: 'Policy & Legal',
      date: '2023-08-05',
      fileSize: '1.2 MB',
      downloadCount: 445,
      featured: false,
      url: '/resources/benefit-sharing-framework.pdf',
      tags: ['policy', 'benefit-sharing', 'community rights', 'legal framework'],
    },
    {
      id: 'medicinal-plants-research-findings',
      title: 'Medicinal Plants Research Findings 2023',
      description:
        'Latest research findings on the efficacy and safety of traditional medicinal plants from various Kenyan communities.',
      type: 'research',
      category: 'Scientific Research',
      date: '2023-07-18',
      fileSize: '4.1 MB',
      downloadCount: 1120,
      featured: false,
      url: '/resources/medicinal-plants-research-2023.pdf',
      tags: ['medicinal plants', 'research', 'efficacy', 'safety'],
    },
    {
      id: 'entrepreneurship-toolkit',
      title: 'Natural Products Entrepreneurship Toolkit',
      description:
        'Comprehensive toolkit for entrepreneurs looking to start natural products businesses, including templates and checklists.',
      type: 'toolkit',
      category: 'Business Development',
      date: '2023-06-12',
      fileSize: '2.8 MB',
      downloadCount: 780,
      featured: true,
      url: '/resources/entrepreneurship-toolkit.pdf',
      tags: ['entrepreneurship', 'business development', 'toolkit', 'templates'],
    },
    {
      id: 'npi-annual-report-2023',
      title: 'NPI Annual Report 2023',
      description:
        "Comprehensive annual report showcasing NPI's achievements, impact, and financial performance for 2023.",
      type: 'report',
      category: 'Annual Reports',
      date: '2024-03-01',
      fileSize: '5.5 MB',
      downloadCount: 920,
      featured: true,
      url: '/resources/annual-report-2023.pdf',
      tags: ['annual report', 'achievements', 'impact', 'financial performance'],
    },
    {
      id: 'ip-protection-guidelines',
      title: 'Intellectual Property Protection Guidelines',
      description:
        'Guidelines for protecting traditional knowledge intellectual property rights and navigating legal frameworks.',
      type: 'guide',
      category: 'Legal & IP',
      date: '2023-05-20',
      fileSize: '1.5 MB',
      downloadCount: 560,
      featured: false,
      url: '/resources/ip-protection-guidelines.pdf',
      tags: ['intellectual property', 'legal protection', 'guidelines', 'rights'],
    },
    {
      id: 'sustainability-practices-manual',
      title: 'Sustainable Harvesting Practices Manual',
      description:
        'Best practices manual for sustainable harvesting of natural resources while maintaining ecological balance.',
      type: 'guide',
      category: 'Environmental Conservation',
      date: '2023-04-15',
      fileSize: '2.1 MB',
      downloadCount: 650,
      featured: false,
      url: '/resources/sustainability-practices-manual.pdf',
      tags: ['sustainability', 'harvesting', 'conservation', 'best practices'],
    },
    {
      id: 'community-engagement-video',
      title: 'Community Engagement Best Practices',
      description:
        'Video presentation on effective community engagement strategies and cultural sensitivity in natural products development.',
      type: 'video',
      category: 'Training Materials',
      date: '2023-03-10',
      fileSize: '150 MB',
      downloadCount: 340,
      featured: false,
      url: '/resources/videos/community-engagement.mp4',
      tags: ['video', 'community engagement', 'training', 'cultural sensitivity'],
    },
  ],
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedType, setSelectedType] = useState('All Types')

  const categories = ['All Categories', ...Array.from(new Set(resources.map((r) => r.category)))]
  const types = ['All Types', ...Array.from(new Set(resources.map((r) => r.type)))]

  const filteredResources = resources.filter((resource) => {
    const matchesSearch =
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory =
      selectedCategory === 'All Categories' || resource.category === selectedCategory
    const matchesType = selectedType === 'All Types' || resource.type === selectedType

    return matchesSearch && matchesCategory && matchesType
  })

  const featuredResources = filteredResources.filter((r) => r.featured)
  const regularResources = filteredResources.filter((r) => !r.featured)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-5 h-5" />
      case 'presentation':
        return <Image className="w-5 h-5" />
      default:
        return <FileText className="w-5 h-5" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'report':
        return 'bg-[#25718A] text-white'
      case 'guide':
        return 'bg-[#725242] text-white'
      case 'policy':
        return 'bg-[#8A3E25] text-white'
      case 'research':
        return 'bg-[#25718A] text-white'
      case 'video':
        return 'bg-[#8A3E25] text-white'
      case 'presentation':
        return 'bg-[#725242] text-white'
      case 'toolkit':
        return 'bg-[#25718A] text-white'
      default:
        return 'bg-[#725242] text-white'
    }
  }

  return (
    <NPISection className="bg-[#FFFFFF]">
      <NPISectionHeader>
        <NPISectionTitle className="text-black">{title}</NPISectionTitle>
        <NPISectionDescription className="text-black">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Search and Filters */}
      <NPICard
        className="mb-8 border-[#725242] shadow-lg"
        style={{ borderRadius: '0', backgroundColor: '#FFFFFF' }}
      >
        <NPICardContent className="p-6">
          {/* Search Bar */}
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#725242] w-5 h-5" />
              <input
                type="text"
                placeholder="Search resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] focus:border-[#25718A] font-npi text-black bg-[#EFE3BA]"
                style={{ borderRadius: '0' }}
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-[#725242]" />
            <span className="font-medium font-npi text-black">Filter by:</span>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#725242]">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] font-npi text-black bg-[#EFE3BA]"
                style={{ borderRadius: '0' }}
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-[#725242]">Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full p-2 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] font-npi text-black bg-[#EFE3BA]"
                style={{ borderRadius: '0' }}
              >
                {types.map((type) => (
                  <option key={type} value={type}>
                    {type === 'All Types' ? type : type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Resources */}
      {featuredResources.length > 0 && (
        <div className="mb-12" id="featured-resources">
          <h3 className="text-2xl font-bold mb-6 font-npi flex items-center gap-2 text-black">
            <BookOpen className="w-6 h-6 text-[#725242]" />
            Featured Resources
          </h3>
          <div className="grid grid-cols-4 gap-6">
            {featuredResources.slice(0, 4).map((resource) => (
              <NPICard
                key={resource.id}
                className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-[#725242] border-2 border-transparent flex flex-col h-auto min-h-[500px]"
                style={{ backgroundColor: '#FFFFFF' }}
              >
                <NPICardHeader className="flex-shrink-0">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-8 h-8 bg-[#725242]/10 flex items-center justify-center text-[#725242]"
                        style={{ borderRadius: '0' }}
                      >
                        {getTypeIcon(resource.type)}
                      </div>
                      <div>
                        <span
                          className={`px-2 py-1 text-xs font-medium ${getTypeColor(resource.type)}`}
                          style={{ borderRadius: '0' }}
                        >
                          {resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                        </span>
                        <div className="text-xs text-[#725242] mt-1 font-npi">
                          {resource.category}
                        </div>
                      </div>
                    </div>
                    <span
                      className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium"
                      style={{ borderRadius: '0' }}
                    >
                      Featured
                    </span>
                  </div>
                  <NPICardTitle className="text-base leading-tight text-black mb-2">
                    {resource.title}
                  </NPICardTitle>
                </NPICardHeader>

                <NPICardContent className="flex-grow flex flex-col">
                  <p className="text-[#725242] mb-4 leading-relaxed font-npi text-sm flex-grow">
                    {resource.description}
                  </p>

                  <div className="flex items-center gap-2 text-xs text-[#725242] mb-3">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {new Date(resource.date).toLocaleDateString()}
                    </span>
                    {resource.fileSize && (
                      <>
                        <span>•</span>
                        <span>{resource.fileSize}</span>
                      </>
                    )}
                  </div>

                  <div className="flex items-center gap-2 text-xs text-[#725242] mb-3">
                    <span className="flex items-center gap-1">
                      <Download className="w-3 h-3" />
                      {resource.downloadCount.toLocaleString()} downloads
                    </span>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {resource.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-[#EFE3BA] text-[#725242] text-xs font-npi"
                        style={{ borderRadius: '0' }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </NPICardContent>

                <NPICardFooter className="flex gap-2 flex-shrink-0">
                  <NPIButton
                    asChild
                    variant="primary"
                    className="flex-1 bg-[#25718A] hover:bg-[#8A3E25] text-white"
                    style={{ borderRadius: '0' }}
                  >
                    <Link href={resource.url} target="_blank">
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Link>
                  </NPIButton>
                  {resource.previewUrl && (
                    <NPIButton
                      asChild
                      variant="outline"
                      size="sm"
                      className="border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white"
                      style={{ borderRadius: '0' }}
                    >
                      <Link href={resource.previewUrl} target="_blank">
                        <Eye className="w-4 h-4" />
                      </Link>
                    </NPIButton>
                  )}
                </NPICardFooter>
              </NPICard>
            ))}
          </div>
        </div>
      )}

      {/* Regular Resources */}
      <div className="grid grid-cols-4 gap-6">
        {regularResources.map((resource) => (
          <NPICard
            key={resource.id}
            className="hover:shadow-lg transition-all duration-300 hover:scale-105 hover:border-[#725242] border-2 border-transparent aspect-square flex flex-col"
            style={{ backgroundColor: '#EFE3BA' }}
          >
            <NPICardHeader className="flex-shrink-0">
              <div className="flex items-center gap-3 mb-3">
                <div
                  className="w-8 h-8 bg-[#725242]/10 flex items-center justify-center text-[#725242]"
                  style={{ borderRadius: '0' }}
                >
                  {getTypeIcon(resource.type)}
                </div>
                <div>
                  <span
                    className={`px-2 py-1 text-xs font-medium ${getTypeColor(resource.type)}`}
                    style={{ borderRadius: '0' }}
                  >
                    {resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                  </span>
                </div>
              </div>
              <NPICardTitle className="text-lg leading-tight text-black line-clamp-2">
                {resource.title}
              </NPICardTitle>
              <div className="text-sm text-[#725242] font-medium font-npi">{resource.category}</div>
            </NPICardHeader>

            <NPICardContent className="flex-grow flex flex-col">
              <p className="text-[#725242] text-sm leading-relaxed mb-4 font-npi line-clamp-3 flex-grow">
                {resource.description.substring(0, 120)}...
              </p>

              <div className="flex items-center gap-2 text-xs text-[#725242] mb-3">
                <Calendar className="w-3 h-3" />
                <span>{new Date(resource.date).toLocaleDateString()}</span>
                {resource.fileSize && (
                  <>
                    <span>•</span>
                    <span>{resource.fileSize}</span>
                  </>
                )}
              </div>

              <div className="text-xs text-[#725242] font-npi">
                {resource.downloadCount.toLocaleString()} downloads
              </div>
            </NPICardContent>

            <NPICardFooter className="flex gap-2 flex-shrink-0">
              <NPIButton
                asChild
                variant="outline"
                className="flex-1 border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white"
                size="sm"
                style={{ borderRadius: '0' }}
              >
                <Link href={resource.url} target="_blank">
                  <Download className="w-3 h-3 mr-2" />
                  Download
                </Link>
              </NPIButton>
              {resource.previewUrl && (
                <NPIButton
                  asChild
                  variant="ghost"
                  size="sm"
                  className="text-[#25718A] hover:bg-[#25718A]/10"
                  style={{ borderRadius: '0' }}
                >
                  <Link href={resource.previewUrl} target="_blank">
                    <Eye className="w-3 h-3" />
                  </Link>
                </NPIButton>
              )}
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-[#725242] font-npi">
          Showing {filteredResources.length} of {resources.length} resources
        </p>
      </div>
    </NPISection>
  )
}
