import React from 'react'
import {
  NPISec<PERSON>,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Target, TrendingUp, Globe, Users, ArrowRight, ExternalLink } from 'lucide-react'

interface AlignmentItem {
  title: string
  description: string
  icon: React.ReactNode
  color: string
  link?: string
  keyPoints: string[]
}

interface NPIStrategicAlignmentProps {
  title?: string
  description?: string
  alignments?: AlignmentItem[]
}

export const NPIStrategicAlignmentBlock: React.FC<NPIStrategicAlignmentProps> = ({
  title = 'Strategic Alignment',
  description = "NPI is strategically aligned with Kenya's key development frameworks, ensuring coordinated efforts toward national transformation goals.",
  alignments = [
    {
      title: 'Kenya Vision 2030',
      description:
        "Aligned with the Economic Pillar's focus on transforming Kenya into a newly industrializing, middle-income country.",
      icon: <Target className="w-8 h-8" />,
      color: 'from-[#8A3E25] to-[#725242]',
      link: 'https://vision2030.go.ke',
      keyPoints: [
        'Economic transformation through industrialization',
        'Value addition to natural resources',
        'Innovation and technology adoption',
        'Sustainable development practices',
      ],
    },
    {
      title: 'Medium Term Plan IV (MTP IV)',
      description:
        "Directly contributes to MTP IV's emphasis on manufacturing, innovation, and value addition to natural resources.",
      icon: <TrendingUp className="w-8 h-8" />,
      color: 'from-[#8A6240] to-[#CABA9C]',
      keyPoints: [
        'Manufacturing sector development',
        'Innovation and research promotion',
        'Natural resource value addition',
        'Job creation and economic growth',
      ],
    },
    {
      title: 'Bioinnovate Africa (BeTA)',
      description:
        "Leverages BeTA's platform for bioscience innovation and regional collaboration in natural products development.",
      icon: <Globe className="w-8 h-8" />,
      color: 'from-[#34170D] to-[#4D2D18]',
      link: 'https://bioinnovate-africa.org',
      keyPoints: [
        'Regional bioscience collaboration',
        'Innovation ecosystem development',
        'Technology transfer facilitation',
        'Capacity building programs',
      ],
    },
    {
      title: 'National Museums of Kenya Strategic Plan',
      description:
        "Integrates with NMK's mandate for cultural heritage preservation and knowledge documentation.",
      icon: <Users className="w-8 h-8" />,
      color: 'from-[#25718A] to-[#725242]',
      link: 'https://museums.or.ke',
      keyPoints: [
        'Cultural heritage preservation',
        'Indigenous knowledge documentation',
        'Community engagement programs',
        'Educational outreach initiatives',
      ],
    },
  ],
}) => {
  return (
    <NPISection
      size="tight"
      className="bg-gradient-to-br from-[#FFFFFF] via-[#EFE3BA] to-[#725242]"
    >
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      <div className="grid lg:grid-cols-2 gap-8">
        {alignments.map((alignment, index) => (
          <NPICard
            key={index}
            className="overflow-hidden hover:shadow-xl transition-all duration-300"
          >
            {/* Header with gradient */}
            <div className={`bg-gradient-to-r ${alignment.color} p-6 text-white`}>
              <div className="flex items-center gap-4 mb-4">
                <div className="bg-white/20 p-3 rounded-lg">{alignment.icon}</div>
                <div>
                  <NPICardTitle className="text-white text-xl mb-2">{alignment.title}</NPICardTitle>
                  {alignment.link && (
                    <Link
                      href={alignment.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-white/80 hover:text-white text-sm transition-colors"
                    >
                      Visit Website <ExternalLink className="w-4 h-4 ml-1" />
                    </Link>
                  )}
                </div>
              </div>
              <p className="text-white/90 leading-relaxed font-npi">{alignment.description}</p>
            </div>

            {/* Content */}
            <NPICardContent className="p-6">
              <h4 className="font-semibold mb-4 text-foreground font-npi">Key Alignment Areas:</h4>
              <ul className="space-y-3">
                {alignment.keyPoints.map((point, pointIndex) => (
                  <li key={pointIndex} className="flex items-start gap-3">
                    <ArrowRight className="w-4 h-4 text-primary mt-1 flex-shrink-0" />
                    <span className="text-muted-foreground font-npi">{point}</span>
                  </li>
                ))}
              </ul>
            </NPICardContent>
          </NPICard>
        ))}
      </div>

      {/* Integration Summary */}
      <div className="mt-16">
        <NPICard className="bg-gradient-to-r from-primary/5 via-secondary/5 to-accent/5 border-2 border-primary/20">
          <NPICardHeader>
            <NPICardTitle className="text-2xl text-center text-primary">
              Integrated Approach
            </NPICardTitle>
          </NPICardHeader>
          <NPICardContent>
            <div className="text-center max-w-4xl mx-auto">
              <p className="text-lg leading-relaxed mb-6 font-npi">
                Through strategic alignment with these key frameworks, NPI ensures that natural
                products development contributes meaningfully to Kenya&apos;s transformation agenda
                while maintaining coherence with national priorities and international best
                practices.
              </p>

              <div className="grid md:grid-cols-3 gap-6 mt-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Target className="w-8 h-8 text-primary" />
                  </div>
                  <h5 className="font-semibold mb-2 font-npi">Policy Coherence</h5>
                  <p className="text-sm text-muted-foreground font-npi">
                    Aligned with national development policies
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Users className="w-8 h-8 text-secondary" />
                  </div>
                  <h5 className="font-semibold mb-2 font-npi">Multi-Agency Coordination</h5>
                  <p className="text-sm text-muted-foreground font-npi">
                    Collaborative implementation approach
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="w-8 h-8 text-accent" />
                  </div>
                  <h5 className="font-semibold mb-2 font-npi">Impact Maximization</h5>
                  <p className="text-sm text-muted-foreground font-npi">
                    Leveraging synergies for greater impact
                  </p>
                </div>
              </div>
            </div>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
