'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIPillarModal, type StrategicPillar } from '@/components/ui/npi-modal'
import { StaggerContainer, StaggerItem } from '@/components/ui/npi-scroll-reveal'
import { Users, Lightbulb, Database, Shield } from 'lucide-react'

interface NPIStrategicPillarsProps {
  title?: string
  description?: string
  pillars?: StrategicPillar[]
}

export const NPIStrategicPillarsBlock: React.FC<NPIStrategicPillarsProps> = ({
  title = 'Strategic Pillars',
  description = "Four interconnected pillars that form the foundation of NPI's comprehensive approach to natural products development.",
  pillars = [
    {
      id: 'knowledge-documentation',
      title: 'Indigenous Knowledge Documentation',
      description:
        "Systematic collection, preservation, and digitization of traditional knowledge systems across Kenya's 47 counties.",
      icon: <Database className="w-8 h-8" />,
      color: 'from-[#8A3E25] to-[#725242]',
      objectives: [
        'Document indigenous knowledge from all 47 counties',
        'Establish standardized documentation protocols',
        'Create comprehensive digital knowledge repository',
        'Ensure community ownership and benefit-sharing',
      ],
      keyActivities: [
        'Community engagement and knowledge collection',
        'Digital documentation and preservation',
        'Knowledge validation and verification',
        'Community benefit-sharing protocols',
      ],
      milestones: [
        { title: 'Documentation Framework Established', status: 'completed', date: '2023-Q2' },
        { title: '1000+ Knowledge Assets Documented', status: 'completed', date: '2023-Q4' },
        { title: 'All Counties Coverage', status: 'in-progress', date: '2024-Q2' },
        { title: 'Digital Archive Completion', status: 'planned', date: '2024-Q4' },
      ],
      relatedProjects: [
        {
          title: 'Knowledge Documentation Platform',
          status: 'active',
          link: '/projects/documentation-platform',
        },
        {
          title: 'Community Documentation Training',
          status: 'active',
          link: '/projects/documentation-training',
        },
        {
          title: 'Digital Preservation Initiative',
          status: 'upcoming',
          link: '/projects/digital-preservation',
        },
      ],
    },
    {
      id: 'product-development',
      title: 'Product Development & Commercialization',
      description:
        'Transforming traditional knowledge into market-ready products through innovation, research, and community-based development.',
      icon: <Lightbulb className="w-8 h-8" />,
      color: 'from-[#725242] to-[#EFE3BA]',
      objectives: [
        'Develop market-ready natural products',
        'Support community-based enterprises',
        'Facilitate technology transfer and innovation',
        'Create sustainable value chains',
      ],
      keyActivities: [
        'Product research and development',
        'Community enterprise support',
        'Market linkage facilitation',
        'Quality standards development',
      ],
      milestones: [
        { title: 'Innovation Hub Establishment', status: 'completed', date: '2023-Q1' },
        { title: '50+ Products in Development', status: 'completed', date: '2023-Q3' },
        { title: 'Market Access Partnerships', status: 'in-progress', date: '2024-Q1' },
        { title: 'Export Market Entry', status: 'planned', date: '2024-Q3' },
      ],
      relatedProjects: [
        {
          title: 'Community Product Development',
          status: 'active',
          link: '/projects/community-products',
        },
        { title: 'Innovation Hub Network', status: 'active', link: '/projects/innovation-hubs' },
        { title: 'Market Access Program', status: 'upcoming', link: '/projects/market-access' },
      ],
    },
    {
      id: 'capacity-building',
      title: 'Capacity Building & Empowerment',
      description:
        'Empowering communities, especially women and youth, with skills, knowledge, and resources for sustainable natural products development.',
      icon: <Users className="w-8 h-8" />,
      color: 'from-[#25718A] to-[#725242]',
      objectives: [
        'Build community technical capacity',
        'Empower women and youth entrepreneurs',
        'Develop local leadership and governance',
        'Strengthen institutional capabilities',
      ],
      keyActivities: [
        'Training and skills development programs',
        'Women and youth empowerment initiatives',
        'Leadership development programs',
        'Institutional capacity strengthening',
      ],
      milestones: [
        { title: 'Training Centers Established', status: 'completed', date: '2023-Q2' },
        { title: '2000+ Community Members Trained', status: 'completed', date: '2023-Q4' },
        { title: 'Youth Enterprise Program Launch', status: 'in-progress', date: '2024-Q1' },
        { title: 'Advanced Skills Certification', status: 'planned', date: '2024-Q3' },
      ],
      relatedProjects: [
        {
          title: "Women's Empowerment Program",
          status: 'active',
          link: '/projects/women-empowerment',
        },
        {
          title: 'Youth Enterprise Initiative',
          status: 'active',
          link: '/projects/youth-enterprise',
        },
        {
          title: 'Leadership Development Program',
          status: 'upcoming',
          link: '/projects/leadership-development',
        },
      ],
    },
    {
      id: 'ip-protection',
      title: 'Intellectual Property Protection',
      description:
        'Safeguarding traditional knowledge and supporting communities in protecting their intellectual property rights and ensuring fair benefit-sharing.',
      icon: <Shield className="w-8 h-8" />,
      color: 'from-[#8A3E25] to-[#25718A]',
      objectives: [
        'Protect traditional knowledge rights',
        'Support IP registration processes',
        'Ensure fair benefit-sharing mechanisms',
        'Build IP awareness and capacity',
      ],
      keyActivities: [
        'IP rights education and awareness',
        'Traditional knowledge protection protocols',
        'Legal support and registration assistance',
        'Benefit-sharing framework development',
      ],
      milestones: [
        { title: 'IP Protection Framework', status: 'completed', date: '2023-Q1' },
        { title: 'Community IP Training Programs', status: 'completed', date: '2023-Q3' },
        { title: 'First Traditional Medicine Patents', status: 'in-progress', date: '2024-Q1' },
        { title: 'Regional IP Collaboration', status: 'planned', date: '2024-Q4' },
      ],
      relatedProjects: [
        {
          title: 'Traditional Knowledge Protection',
          status: 'active',
          link: '/projects/tk-protection',
        },
        { title: 'IP Registration Support', status: 'active', link: '/projects/ip-registration' },
        {
          title: 'Benefit-Sharing Framework',
          status: 'upcoming',
          link: '/projects/benefit-sharing',
        },
      ],
    },
  ],
}) => {
  const [selectedPillar, setSelectedPillar] = useState<StrategicPillar | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handlePillarClick = (pillar: StrategicPillar) => {
    setSelectedPillar(pillar)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedPillar(null)
  }

  return (
    <NPISection size="tight" className="bg-gradient-to-br from-[#FFFFFF] to-[#EFE3BA] -mt-1">
      <NPISectionHeader>
        <NPISectionTitle className="text-black">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Pillar Cards Grid */}
      <StaggerContainer>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {pillars.map((pillar) => (
            <StaggerItem key={pillar.id} direction="up">
              <NPICard
                className="hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 cursor-pointer h-full bg-gradient-to-br from-white via-[#EFE3BA] to-[#FFFFFF] border-2 border-[#725242]/30 hover:border-[#8A3E25]/50 group overflow-hidden"
                onClick={() => handlePillarClick(pillar)}
              >
                <NPICardHeader className="p-0">
                  <div
                    className={`bg-gradient-to-r ${pillar.color} p-6 text-white group-hover:scale-105 transition-transform duration-500 relative overflow-hidden`}
                  >
                    {/* Background pattern */}
                    <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div className="relative z-10">
                      <div className="flex items-center justify-center mb-4">
                        <div className="bg-white/20 p-4 shadow-xl border border-white/30 group-hover:bg-white/30 transition-colors duration-300">
                          {pillar.icon}
                        </div>
                      </div>
                      <NPICardTitle className="text-white text-center text-lg font-bold leading-tight min-h-[3rem] flex items-center justify-center">
                        {pillar.title}
                      </NPICardTitle>
                    </div>
                  </div>
                </NPICardHeader>
                <NPICardContent className="p-6 flex-1 flex flex-col">
                  <p className="text-[#725242] text-sm leading-relaxed mb-6 flex-1 min-h-[4rem]">
                    {pillar.description}
                  </p>
                  <div className="text-center mt-auto">
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#8A3E25]/10 border border-[#8A3E25]/20 group-hover:bg-[#8A3E25]/20 group-hover:border-[#8A3E25]/40 transition-all duration-300">
                      <span className="text-[#8A3E25] text-sm font-medium group-hover:text-[#25718A] transition-colors">
                        Explore Details
                      </span>
                      <span className="text-[#8A3E25] group-hover:translate-x-1 transition-transform duration-300">
                        →
                      </span>
                    </div>
                  </div>
                </NPICardContent>
              </NPICard>
            </StaggerItem>
          ))}
        </div>
      </StaggerContainer>

      {/* Pillar Modal */}
      <NPIPillarModal isOpen={isModalOpen} onClose={handleCloseModal} pillar={selectedPillar} />
    </NPISection>
  )
}
